import { useTranslations } from "next-intl";
import Image from "next/image";
import mainBackground from "../../public/main_background.png";
import imagePresenter from "../../public/image_presenter3.png";

function BannerHome() {
  const t = useTranslations("metadata");

  return (
    <>
      <div className="h-screen relative">
        {/* Layer 1: รูปภาพพื้นหลัง */}
        <Image
          className="brightness-50 object-cover h-screen w-full absolute inset-0 z-0"
          alt="main_background"
          src={mainBackground}
          priority
          fill
        />

        {/* Layer 2: Gradient ซ้ายเข้ม */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/30 to-transparent z-10"></div>

        {/* Layer 3: ตัวหนังสือและเนื้อหา */}
        <div className="relative flex h-full mx-8 z-20">
          <div className="flex">
            <div className="mx-auto text-center sm:mx-0 sm:my-auto sm:text-left">
              <p className="text-gold-gradient text-2xl sm:text-6xl py-4 mt-[80px] sm:mt-0">
                GruChangThai Antique
              </p>
              <p className="text-gold-gradient text-2xl sm:text-6xl py-4">
                Gold Jewelry
              </p>
              <p className="text-white mt-4 text-xl">
                The Perfect Jewels for you
              </p>
              <button className="button-gold-gradient text-xl mt-8">
                Explore More
              </button>
              <p className="text-white mt-8 text-xl">Call: 084-8047253</p>
            </div>
          </div>
        </div>

        {/* Layer 4: รูปภาพ presenter */}
        <Image
          className="object-contain w-full h-auto absolute bottom-0 left-0 z-30 sm:h-screen sm:w-auto sm:right-8 sm:top-0 sm:left-auto sm:bottom-auto"
          alt="image_presenter"
          src={imagePresenter}
        />
      </div>
    </>
  );
}
export default BannerHome;
