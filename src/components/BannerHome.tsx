import { useTranslations } from "next-intl";
import Image from "next/image";
import mainBackground from "../../public/main_background.png";
import imagePresenter from "../../public/image_presenter3.png";

function BannerHome() {
  const t = useTranslations("metadata");

  return (
    <>
      <div className="h-screen relative">
        {/* Layer 1: รูปภาพพื้นหลัง */}
        <Image
          className="brightness-50 object-cover h-screen w-full absolute inset-0 z-0"
          alt="main_background"
          src={mainBackground}
          priority
          fill
        />

        {/* Layer 2: Gradient ซ้ายเข้ม */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/30 to-transparent z-10"></div>

        {/* Layer 3: ตัวหนังสือและเนื้อหา */}
        <div className="relative flex h-full z-40">
          <div className="flex items-start justify-center w-full pt-24 sm:items-center sm:justify-start sm:mx-8 sm:pt-0">
            <div className="text-center sm:text-left">
              <p className="text-2xl sm:text-6xl py-4 font-bold" style={{
                background: 'linear-gradient(135deg, #FFFF80 0%, #FFE55C 15%, #FFFACD 25%, #FFD700 35%, #FFEC8C 50%, #FFF700 65%, #FFFF99 80%, #FFFEF7 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                textShadow: '1px 1px 0px rgba(0,0,0,0.2), 0px 0px 10px rgba(255,255,0,0.3)'
              }}>
                GruChangThai Antique
              </p>
              <p className="text-2xl sm:text-6xl py-4 font-bold" style={{
                background: 'linear-gradient(135deg, #FFFF80 0%, #FFE55C 15%, #FFFACD 25%, #FFD700 35%, #FFEC8C 50%, #FFF700 65%, #FFFF99 80%, #FFFEF7 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                textShadow: '1px 1px 0px rgba(0,0,0,0.2), 0px 0px 10px rgba(255,255,0,0.3)'
              }}>
                Gold Jewelry
              </p>
              <p className="text-white mt-4 text-xl" style={{textShadow: '1px 1px 0px rgba(0,0,0,0.6)'}}>
                The Perfect Jewels for you
              </p>
              <button className="text-xl mt-8 px-6 py-3 rounded-lg font-semibold hover:scale-105 transition-all duration-300" style={{
                background: 'linear-gradient(135deg, #FFFF80 0%, #FFE55C 25%, #FFF700 50%, #FFEC8C 75%, #FFFEF7 100%)',
                color: '#8B4513',
                textShadow: '1px 1px 0px rgba(255,255,255,0.5)',
                boxShadow: '0 4px 15px rgba(255, 255, 0, 0.5)'
              }}>
                Explore More
              </button>
              <p className="text-white mt-8 text-xl" style={{textShadow: '1px 1px 0px rgba(0,0,0,0.6)'}}>Call: 084-8047253</p>
            </div>
          </div>
        </div>

        {/* Layer 4: รูปภาพ presenter */}
        <Image
          className="object-contain w-full h-auto absolute bottom-0 left-0 z-30 sm:h-auto sm:max-h-full sm:w-auto sm:right-8 sm:bottom-0 sm:left-auto sm:top-auto"
          alt="image_presenter"
          src={imagePresenter}
        />
      </div>
    </>
  );
}
export default BannerHome;
